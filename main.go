package main

import (
	"cloud.google.com/go/auth/credentials"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"google.golang.org/genai"
)

var (
	ProjectID = "aile-ai-development"
	Region    = "global"
	Model     = "gemini-2.5-flash"
)

func main() {
	ctx := gctx.New()
	detectDefault, err := credentials.DetectDefault(&credentials.DetectOptions{
		Scopes:          []string{"https://www.googleapis.com/auth/cloud-platform"},
		CredentialsFile: "key.json",
	})
	if err != nil {
		panic(err)
	}
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		Project:     ProjectID,
		Location:    Region,
		Credentials: detectDefault,
		Backend:     genai.BackendVertexAI,
	})
	if err != nil {
		panic(err)
	}

	catch, err := client.Caches.Create(ctx, Model, &genai.CreateCachedContentConfig{
		DisplayName: "ytCatch",
		SystemInstruction: &genai.Content{
			Parts: []*genai.Part{&genai.Part{Text: "你是一個視頻分析專家， 你所有的回答"}},
			Role:  genai.RoleUser,
		},
		Contents: []*genai.Content{
			genai.NewContentFromURI("https://www.youtube.com/watch?v=qSoIrKQs8Eo&t=1507s", "video/*", genai.RoleUser),
		},
	})

	if err != nil {
		panic(err)
	}
	generateConfig := &genai.GenerateContentConfig{
		Temperature:       genai.Ptr[float32](0.0),
		MaxOutputTokens:   4096,
		ResponseMIMEType:  "application/json",
		SystemInstruction: genai.NewContentFromText("你的回答都是基於我提供給你的資料。資料外的內容請委婉的拒絕", genai.RoleUser),
		CachedContent:     catch.DisplayName,
	}
	chat, err := client.Chats.Create(ctx, Model, generateConfig, nil)
	if err != nil {
		panic(err)
	}
	resp, err := chat.SendMessage(ctx, *genai.NewPartFromText("視頻講述了什麼？"))
	if err != nil {
		panic(err)
	}
	g.Dump(resp)
}
